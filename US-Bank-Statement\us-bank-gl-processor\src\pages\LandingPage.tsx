import React, { useState, useEffect, useCallback } from 'react';
import { Container, Box, Typography, Paper, Alert, Snackbar } from '@mui/material';
import Header from '../components/Header';
import FileUploader from '../components/FileUploader';
import HistoryPanel from '../components/HistoryPanel';
import type { HistoryItem } from '../components/HistoryPanel';

// Add to global type declaration
declare global {
  interface Window {
    isFileProcessing?: boolean;
    downloadSelectedHistoryItem?: () => void;
    fileDetailsForDownload?: { status: string };
  }
}

// Generate a unique ID
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

const LandingPage: React.FC = () => {
  // State for history items
  const [userHistory, setUserHistory] = useState<HistoryItem[]>(() => {
    // Try to load history from localStorage
    const savedHistory = localStorage.getItem('uploadHistory');
    if (savedHistory) {
      try {
        // Parse the saved history and convert date strings back to Date objects
        const parsed = JSON.parse(savedHistory);
        return parsed.map((item: any) => ({
          ...item,
          uploadDate: new Date(item.uploadDate),
          isSystemHistory: false
        }));
      } catch (error) {
        console.error('Error parsing history from localStorage:', error);
        return [];
      }
    }
    return [];
  });

  // State for system history items
  const [systemHistory, setSystemHistory] = useState<HistoryItem[]>([]);

  // Combined history for display
  const [history, setHistory] = useState<HistoryItem[]>([]);

  // State for selected history item
  const [selectedItemId, setSelectedItemId] = useState<string | undefined>(undefined);

  // State for Excel file availability (used internally by the API)

  // State for additional file details
  const [fileDetails, setFileDetails] = useState<{
    inputFileName: string;
    inputFileSize: string;
    processDateTime: string;
    status: string;
    pageCount?: number;
  } | null>(null);

  // State for error messages
  const [error, setError] = useState<string | null>(null);
  const [showError, setShowError] = useState<boolean>(false);

  // Track which file is currently being processed
  const [processingFileId, setProcessingFileId] = useState<string | null>(null);

  // Save user history to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('uploadHistory', JSON.stringify(userHistory));
  }, [userHistory]);

  // Combine user and system history
  useEffect(() => {
    setHistory([...userHistory, ...systemHistory]);
  }, [userHistory, systemHistory]);

  // Fetch system history from API
  const fetchSystemHistory = useCallback(async () => {
    try {
      const response = await fetch('http://192.168.1.15:5000/api/history');

      if (!response.ok) {
        throw new Error(`Failed to fetch system history: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.history) {
        // Convert API response to HistoryItem format
        const systemItems: HistoryItem[] = data.history.map((item: any) => ({
          id: item.id,
          fileName: item.fileName,
          uploadDate: item.uploadDate,
          fileSize: item.fileSize,
          pageCount: item.pageCount,
          excelPath: item.excelPath,
          isSystemHistory: true
        }));

        setSystemHistory(systemItems);
      }
    } catch (err) {
      console.error('Error fetching system history:', err);
      setError('Failed to fetch system history. System history tab may be unavailable.');
      setShowError(true);
    }
  }, []);

  // Fetch system history on component mount
  useEffect(() => {
    fetchSystemHistory();

    // Refresh system history every 5 seconds
    const intervalId = setInterval(fetchSystemHistory, 5000);

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [fetchSystemHistory]);

  // In handleUpload, after 2 seconds, FileUploader will call window.refreshSystemHistory().
  // Now, in LandingPage, set window.refreshSystemHistory to fetchSystemHistory so FileUploader can trigger it.
  React.useEffect(() => {
    window.refreshSystemHistory = fetchSystemHistory;
    return () => { window.refreshSystemHistory = undefined; };
  }, [fetchSystemHistory]);

  // Show browser warning if a file is under processing
  useEffect(() => {
    // Simplified check for processing state
    let isProcessing = false;

    // Check if processingFileId is set (most direct indicator)
    if (processingFileId) {
      console.log("Processing file detected:", processingFileId);
      isProcessing = true;
    }

    // Also check fileDetails status
    if (fileDetails && fileDetails.status === 'Under Processing') {
      console.log("File status is Under Processing");
      isProcessing = true;
    }

    // Check if any item in history is being processed
    const processingItem = history.find(item =>
      item.id === processingFileId ||
      (fileDetails && item.id === selectedItemId && fileDetails.status === 'Under Processing')
    );

    if (processingItem) {
      console.log("Processing item found in history:", processingItem.fileName);
      isProcessing = true;
    }

    console.log("Is processing:", isProcessing);

    if (isProcessing) {
      // Create a warning message for refresh/navigation
      const warningMessage = 'The file is under processing. Please do not refresh or close the window otherwise the current processing will stop.';

      // Use both methods for maximum browser compatibility
      window.onbeforeunload = (e) => {
        e.preventDefault();
        // Modern browsers don't use returnValue anymore, but we'll set it for older browsers
        // Using any type to avoid TypeScript warnings about deprecated property
        (e as any).returnValue = warningMessage;
        return warningMessage;
      };

      const handleBeforeUnload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
        // Modern browsers don't use returnValue anymore, but we'll set it for older browsers
        // Using any type to avoid TypeScript warnings about deprecated property
        (e as any).returnValue = warningMessage;
        return warningMessage;
      };

      // Add the event listener as a backup
      window.addEventListener('beforeunload', handleBeforeUnload);

      // Clean up on effect cleanup
      return () => {
        window.onbeforeunload = null;
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    } else {
      // No processing file, no need for warning
      window.onbeforeunload = null;

      // Clean up function
      return () => {
        window.onbeforeunload = null;
      };
    }
  }, [processingFileId, fileDetails, history, selectedItemId]);

  // Handler for adding a new item to history
  const addToHistory = useCallback((fileName: string, fileSize?: string) => {
    const newItem: HistoryItem = {
      id: generateId(),
      fileName,
      uploadDate: new Date(),
      fileSize,
      isSystemHistory: false
    };

    setUserHistory(prev => [newItem, ...prev]);
    setSelectedItemId(newItem.id);

    // Mark this file as being processed and set a global flag
    setProcessingFileId(newItem.id);

    // Set a global variable to track processing state
    window.isFileProcessing = true;

    // Ensure the file details show "Under Processing" status
    setFileDetails({
      inputFileName: fileName,
      inputFileSize: fileSize || 'Unknown',
      processDateTime: new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric'
      }).format(new Date()),
      status: 'Under Processing',
      pageCount: undefined // Will be updated when history is fetched
    });

    // Also update the global state
    window.fileDetailsForDownload = { status: 'Under Processing' };

    // Log the processing state for debugging
    console.log("File processing started:", fileName, "ID:", newItem.id, "Status: Under Processing");

    return newItem;
  }, []);

  // Handler for downloading an item from history
  const handleDownloadItem = useCallback(async (item: HistoryItem) => {
    try {
      console.log('Downloading item:', item);

      // For system history items, download from the API
      if (item.isSystemHistory) {
        // First check if the Excel file is available
        try {
          const checkResponse = await fetch(`http://192.168.1.15:5000/api/check-excel/${item.id}`);
          const checkData = await checkResponse.json();

          if (!checkData.available) {
            // File is not available, show error message
            setError('The Excel file is not available for download. The file may still be processing or there was an error during processing.');
            setShowError(true);
            return;
          }

          // File is available, proceed with download
          const downloadUrl = `http://192.168.1.15:5000/api/download/${item.id}`;
          console.log(`Opening download URL: ${downloadUrl}`);

          // Create a hidden iframe for download instead of opening a new window
          // This prevents redirecting to an error page if the file doesn't exist
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          document.body.appendChild(iframe);

          // Set up a timeout to handle download failures
          const timeoutId = setTimeout(() => {
            // If download takes too long, assume it failed
            document.body.removeChild(iframe);
            setError('Download timed out. The file may not be available.');
            setShowError(true);
          }, 10000); // 10 second timeout

          // Set up load event to clear timeout and remove iframe
          iframe.onload = () => {
            clearTimeout(timeoutId);
            document.body.removeChild(iframe);

            // Check if the iframe content indicates an error
            try {
              const iframeContent = iframe.contentWindow?.document.body.textContent;
              if (iframeContent && iframeContent.includes('error')) {
                setError('Error downloading file: ' + iframeContent);
                setShowError(true);
                return;
              }
            } catch (e) {
              // Ignore cross-origin errors
            }

            // Show success message
            setError(null);
            setShowError(false);
          };

          // Set the iframe source to the download URL
          iframe.src = downloadUrl;

        } catch (checkErr) {
          console.error('Error checking file availability:', checkErr);
          setError(`Could not verify file availability: ${checkErr instanceof Error ? checkErr.message : 'Unknown error'}`);
          setShowError(true);
        }
      } else {
        // For user history items, we need to process the file again
        // Inform the user that they need to process the file again
        setError('This file needs to be processed again to download the Excel file.');
        setShowError(true);
      }
    } catch (err) {
      console.error('Error in download handler:', err);
      setError(`Download failed: ${err instanceof Error ? err.message : 'Unknown error'}. Please try again.`);
      setShowError(true);
    }
  }, []);

  // Handler for downloading the PDF of a history item
  const handleDownloadPDFItem = useCallback(async (item: HistoryItem) => {
    try {
      if (item.isSystemHistory) {
        // Download the original PDF from the system history
        const dataDir = 'http://192.168.1.15:5000/api/download-pdf/' + item.id;
        // Open in a hidden iframe to trigger download
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        document.body.appendChild(iframe);
        iframe.src = dataDir;
        setTimeout(() => {
          document.body.removeChild(iframe);
        }, 10000);
      } else {
        setError('PDF download is only available for system history items.');
        setShowError(true);
      }
    } catch {
      setError('Failed to download PDF.');
      setShowError(true);
    }
  }, []);

  // Handler for selecting an item from history
  const handleSelectItem = useCallback(async (item: HistoryItem) => {
    // Immediately set the selected item ID
    setSelectedItemId(item.id);

    // Immediately update the global state for faster UI response
    // Determine initial status based on available information
    let immediateStatus = 'Not Available';
    if (item.excelPath) {
      immediateStatus = 'Available';
    } else if (processingFileId === item.id) {
      const isRecentUpload = item.uploadDate instanceof Date &&
        (new Date().getTime() - item.uploadDate.getTime()) < 15 * 60 * 1000;
      const isRecentSystemUpload = typeof item.uploadDate === 'string' &&
        (new Date().getTime() - new Date(item.uploadDate).getTime()) < 15 * 60 * 1000;

      if (isRecentUpload || isRecentSystemUpload) {
        immediateStatus = 'Under Processing';
      }
    }

    // Update global state immediately
    window.fileDetailsForDownload = { status: immediateStatus };

    // Set file details
    const processDateTime = typeof item.uploadDate === 'string'
      ? item.uploadDate.replace('T', ' ')
      : new Intl.DateTimeFormat('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: 'numeric',
          minute: 'numeric',
          second: 'numeric'
        }).format(item.uploadDate);

    // Check if this is a recently uploaded file (within last 15 minutes)
    const isRecentUpload = item.uploadDate instanceof Date &&
      (new Date().getTime() - item.uploadDate.getTime()) < 15 * 60 * 1000;

    // Check Excel availability for system history items
    if (item.isSystemHistory) {
      try {
        // Call the API to check if Excel file is available
        const response = await fetch(`http://192.168.1.15:5000/api/check-excel/${item.id}`);
        const data = await response.json();

        // Check if this is a recent upload (within last 15 minutes) for system items
        const isRecentSystemUpload = typeof item.uploadDate === 'string'
          ? (new Date().getTime() - new Date(item.uploadDate).getTime()) < 15 * 60 * 1000
          : false;

        // Only show Under Processing for the file that is actually being processed
        const status = data.available
          ? 'Available'
          : (processingFileId === item.id && isRecentSystemUpload)
            ? 'Under Processing'
            : 'Not Available';

        setFileDetails({
          inputFileName: item.fileName,
          inputFileSize: item.fileSize || 'Unknown',
          processDateTime,
          status,
          pageCount: item.pageCount
        });

        // Set global function for download button
        window.downloadSelectedHistoryItem = () => handleDownloadItem(item);

        // If the file is under processing, poll for completion
        if (status === 'Under Processing') {
          const pollInterval = setInterval(async () => {
            try {
              const response = await fetch(`http://192.168.1.15:5000/api/check-excel/${item.id}`);
              const data = await response.json();

              if (data.available) {
                setFileDetails(prev => prev ? {
                  ...prev,
                  status: 'Available'
                } : null);
                setProcessingFileId(null); // Clear processing file
                window.isFileProcessing = false; // Clear global processing flag
                clearInterval(pollInterval);
              } else if (!isRecentSystemUpload) {
                // If more than 15 minutes have passed, stop polling and show Not Available
                setFileDetails(prev => prev ? {
                  ...prev,
                  status: 'Not Available'
                } : null);
                setProcessingFileId(null);
                window.isFileProcessing = false; // Clear global processing flag
                clearInterval(pollInterval);
              }
            } catch (err) {
              console.error('Error polling file status:', err);
              setFileDetails(prev => prev ? {
                ...prev,
                status: 'Error checking availability'
              } : null);
              setProcessingFileId(null);
              window.isFileProcessing = false; // Clear global processing flag
              clearInterval(pollInterval);
            }
          }, 10000); // Poll every 10 seconds

          // Clean up interval on unmount or when selecting a different item
          return () => clearInterval(pollInterval);
        }
      } catch (err) {
        console.error('Error checking Excel availability:', err);
        setFileDetails({
          inputFileName: item.fileName,
          inputFileSize: item.fileSize || 'Unknown',
          processDateTime,
          status: 'Error checking availability',
          pageCount: item.pageCount
        });
      }
    } else {
      // For user history items
      const status = (processingFileId === item.id && isRecentUpload) ? 'Under Processing' : 'Not Available';
      setFileDetails({
        inputFileName: item.fileName,
        inputFileSize: item.fileSize || 'Unknown',
        processDateTime,
        status,
        pageCount: item.pageCount
      });

      // If the file is under processing, poll for completion
      if (status === 'Under Processing') {
        const pollInterval = setInterval(async () => {
          try {
            const response = await fetch(`http://192.168.1.15:5000/api/check-excel/${item.id}`);
            const data = await response.json();

            if (data.available) {
              setFileDetails(prev => prev ? {
                ...prev,
                status: 'Available'
              } : null);
              setProcessingFileId(null);
              window.isFileProcessing = false; // Clear global processing flag
              clearInterval(pollInterval);
            } else if (!isRecentUpload) {
              // If more than 15 minutes have passed, stop polling and show Not Available
              setFileDetails(prev => prev ? {
                ...prev,
                status: 'Not Available'
              } : null);
              setProcessingFileId(null);
              window.isFileProcessing = false; // Clear global processing flag
              clearInterval(pollInterval);
            }
          } catch (err) {
            console.error('Error polling file status:', err);
            setFileDetails(prev => prev ? {
              ...prev,
              status: 'Error checking availability'
            } : null);
            setProcessingFileId(null);
            window.isFileProcessing = false; // Clear global processing flag
            clearInterval(pollInterval);
          }
        }, 10000); // Poll every 10 seconds

        // Clean up interval on unmount or when selecting a different item
        return () => clearInterval(pollInterval);
      }
    }
  }, [handleDownloadItem, processingFileId]);

  // In handleSelectItem, set global PDF download function for FileUploader
  useEffect(() => {
    if (selectedItemId) {
      const selectedItem = history.find(item => item.id === selectedItemId);
      if (selectedItem) {
        window.downloadSelectedHistoryItemPDF = () => handleDownloadPDFItem(selectedItem);
      }
    } else {
      window.downloadSelectedHistoryItemPDF = undefined;
    }
  }, [selectedItemId, history, handleDownloadPDFItem]);

  // Update window.fileDetailsForDownload immediately when fileDetails changes
  useEffect(() => {
    if (fileDetails) {
      // Immediately update the global state for faster UI response
      window.fileDetailsForDownload = { status: fileDetails.status };
    } else {
      window.fileDetailsForDownload = undefined;
    }
  }, [fileDetails]);

  // Also update window.fileDetailsForDownload when selectedItemId changes
  useEffect(() => {
    if (selectedItemId) {
      const selectedItem = history.find(item => item.id === selectedItemId);
      if (selectedItem) {
        // Immediately set status based on the selected item
        const isRecentUpload = selectedItem.uploadDate instanceof Date &&
          (new Date().getTime() - selectedItem.uploadDate.getTime()) < 15 * 60 * 1000;
        const isRecentSystemUpload = typeof selectedItem.uploadDate === 'string' &&
          (new Date().getTime() - new Date(selectedItem.uploadDate).getTime()) < 15 * 60 * 1000;

        // Determine status immediately
        let immediateStatus = 'Not Available';
        if (selectedItem.excelPath) {
          immediateStatus = 'Available';
        } else if (processingFileId === selectedItem.id && (isRecentUpload || isRecentSystemUpload)) {
          immediateStatus = 'Under Processing';
        }

        // Update global state immediately
        window.fileDetailsForDownload = { status: immediateStatus };
      }
    }
  }, [selectedItemId, history, processingFileId]);


  return (
    <Box sx={{ minHeight: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* App header with title - spans the entire width */}
      <Header title="US Bank Statements GL Processor" />

      {/* Main layout with sidebar and content */}
      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, flex: 1 }}>
        {/* Left side - History Panel (full height) */}
        <Box
          sx={{
            width: { xs: '100%', md: '320px' },
            borderRight: { xs: 0, md: 1 },
            borderBottom: { xs: 1, md: 0 },
            borderColor: 'divider',
            height: { xs: 'auto', md: '100%' },
            minHeight: { xs: '300px', md: 'calc(100vh - 64px)' }, // Subtract header height
            display: 'flex',
            flexDirection: 'column',
            bgcolor: (theme) =>
              theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.01)',
            position: { xs: 'static', md: 'sticky' },
            top: 64, // Position below header
            left: 0,
            overflow: 'hidden', // Prevent overflow from affecting the whole page
            maxHeight: { xs: '500px', md: 'calc(100vh - 64px)' }, // Limit height on mobile, full height on desktop
            overscrollBehavior: 'contain' // Prevent scroll chaining
          }}
        >
          <HistoryPanel
            history={history}
            onSelectItem={handleSelectItem}
            onDownloadItem={handleDownloadItem}
            onRefresh={fetchSystemHistory}
            selectedItemId={selectedItemId}
            fileDetails={fileDetails}
          />
        </Box>

        {/* Right side - Content area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>

          {/* Content header */}
          <Box sx={{ py: 3, textAlign: 'center', borderBottom: 1, borderColor: 'divider' }}>
            <Container maxWidth="lg">
              <Typography variant="subtitle1" color="text.secondary" sx={{ maxWidth: 700, mx: 'auto' }}>
                Upload your US Bank statement PDF files to automatically process and convert them to Excel format for General Ledger integration.
              </Typography>
            </Container>
          </Box>

          {/* Main content */}
          <Box sx={{ flex: 1, p: 3, overflowY: 'auto' }}>
            <Container maxWidth="md" sx={{ mx: 'auto', width: '100%' }}>
              <FileUploader
                onFileProcessed={(fileName, fileSize) => {
                  // Add the processed file to history
                  addToHistory(fileName, fileSize);
                }}
                isDownloadState={selectedItemId !== undefined}
                onReset={() => {
                  // Reset the selected item when "Process Another File" is clicked
                  setSelectedItemId(undefined);
                  setFileDetails(null);
                }}
              />

              {/* Display file details when a history item is selected */}
              {fileDetails && (
                <Paper
                  elevation={3}
                  sx={{
                    p: 3,
                    width: '100%',
                    mx: 'auto',
                    borderRadius: 2,
                    position: 'relative',
                    overflow: 'hidden',
                    mb: 3,
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '6px',
                      background: (theme) =>
                        theme.palette.mode === 'light'
                          ? `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
                          : `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                    }
                  }}
                >
                  <Typography variant="h6" gutterBottom color="primary" sx={{ mb: 3 }}>
                    File Details
                  </Typography>
                  <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr' }, gap: 3, mt: 2 }}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>Input File</Typography>
                      <Typography variant="body1" fontWeight="500">{fileDetails.inputFileName}</Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>File Size</Typography>
                      <Typography variant="body1" fontWeight="500">{fileDetails.inputFileSize}</Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>Number of Pages</Typography>
                      <Typography variant="body1" fontWeight="500">
                        {fileDetails.pageCount ? `${fileDetails.pageCount} pages` : 'Unknown'}
                      </Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>Process Date/Time</Typography>
                      <Typography variant="body1" fontWeight="500">{fileDetails.processDateTime}</Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>Status</Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: fileDetails.status === 'Available'
                            ? 'success.main'
                            : fileDetails.status === 'Under Processing'
                            ? 'info.main'
                            : 'warning.main',
                          fontWeight: 'bold',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1
                        }}
                      >
                        {fileDetails.status}
                      </Typography>
                    </Box>
                  </Box>
                </Paper>
              )}

              <Paper
                elevation={3}
                sx={{
                  p: 3,
                  width: '100%',
                  mx: 'auto',
                  borderRadius: 2,
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '6px',
                    background: (theme) =>
                      theme.palette.mode === 'light'
                        ? `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
                        : `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  }
                }}
              >
                <Typography variant="h6" gutterBottom color="primary" sx={{ mb: 3 }}>
                  Note:
                </Typography>
                <Box component="ul" sx={{ pl: 4, m: 0 }}>
                  <Typography component="li" variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    This application processes US Bank statement PDFs and extracts transaction data into a structured Excel format suitable for General Ledger import.
                  </Typography>
                  <Typography component="li" variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Current Support Bank: Belgrade State Bank
                  </Typography>
                  <Typography component="li" variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Supported file formats: PDF
                  </Typography>
                  <Typography component="li" variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Processing will take around 10-15 min.
                  </Typography>
                  <Typography component="li" variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Once completed you will be prompted to download and it will also present here even if you close this window.
                  </Typography>
                </Box>
              </Paper>
            </Container>
          </Box>

          {/* Footer - only in the content area */}
          <Box
            component="footer"
            sx={{
              py: 2,
              textAlign: 'center',
              borderTop: 1,
              borderColor: 'divider'
            }}
          >
            <Typography variant="body2" color="text.secondary">
              © {new Date().getFullYear()} US Bank GL Processing Tool
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Error Snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={() => setShowError(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowError(false)}
          severity="error"
          variant="filled"
          sx={{ width: '100%' }}
        >
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default LandingPage;
